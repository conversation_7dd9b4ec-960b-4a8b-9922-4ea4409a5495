part of 'inventory_bloc.dart';

abstract class InventoryEvent extends Equatable {
  const InventoryEvent();

  @override
  List<Object> get props => [];
}

class LoadProducts extends InventoryEvent {
  const LoadProducts();
}

class AddProduct extends InventoryEvent {
  final ProductModel product;

  const AddProduct(this.product);

  @override
  List<Object> get props => [product];
}

class UpdateProduct extends InventoryEvent {
  final ProductModel product;

  const UpdateProduct(this.product);

  @override
  List<Object> get props => [product];
}

class DeleteProduct extends InventoryEvent {
  final String id;

  const DeleteProduct(this.id);

  @override
  List<Object> get props => [id];
}

class SearchProducts extends InventoryEvent {
  final String query;

  const SearchProducts(this.query);

  @override
  List<Object> get props => [query];
}

class FilterByCategory extends InventoryEvent {
  final String categoryId;

  const FilterByCategory(this.categoryId);

  @override
  List<Object> get props => [categoryId];
}

class LoadLowStockProducts extends InventoryEvent {
  const LoadLowStockProducts();
}