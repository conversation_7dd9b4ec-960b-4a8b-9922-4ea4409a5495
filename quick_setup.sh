#!/bin/bash

# Quick Flutter PATH Setup for Current Session
# Run this to use Flutter immediately without restart

echo "🔧 Setting up Flutter PATH for current session..."

# Add Flutter to PATH for current session
export PATH="$PATH:/tmp/flutter/bin"

# Verify Flutter is accessible
if command -v flutter &> /dev/null; then
    echo "✅ Flutter found!"
    flutter --version
    
    echo "📦 Installing dependencies..."
    cd "/Users/<USER>/Desktop/new kilo"
    flutter pub get
    
    echo "🖥️  Enabling macOS desktop support..."
    flutter config --enable-macos-desktop
    
    echo "🚀 Launching POS Desktop Pro..."
    flutter run -d macos
else
    echo "❌ Flutter not found. Please extract Flutter first:"
    echo "   cd /tmp && unzip -q flutter_macos.zip"
fi