import 'package:flutter/material.dart';
import '../../core/services/barcode_scanner_service.dart';

class BarcodeScannerWidget extends StatefulWidget {
  final Function(String) onBarcodeScanned;
  final String? initialValue;

  const BarcodeScannerWidget({
    super.key,
    required this.onBarcodeScanned,
    this.initialValue,
  });

  @override
  State<BarcodeScannerWidget> createState() => _BarcodeScannerWidgetState();
}

class _BarcodeScannerWidgetState extends State<BarcodeScannerWidget> {
  final TextEditingController _controller = TextEditingController();
  bool _isScanning = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null) {
      _controller.text = widget.initialValue!;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _scanBarcode() async {
    setState(() {
      _isScanning = true;
    });

    try {
      final barcode = await BarcodeScannerService.scanBarcode();
      if (barcode != null && barcode.isNotEmpty) {
        _controller.text = barcode;
        widget.onBarcodeScanned(barcode);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Scanning failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
    }
  }

  void _onManualEntry() {
    final barcode = _controller.text.trim();
    if (barcode.isNotEmpty) {
      if (BarcodeScannerService.isValidBarcode(barcode)) {
        widget.onBarcodeScanned(barcode);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid barcode format'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _generateSampleBarcode() {
    final sampleBarcode = BarcodeScannerService.generateSampleBarcode();
    _controller.text = sampleBarcode;
    widget.onBarcodeScanned(sampleBarcode);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.qr_code_scanner, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                'Barcode Scanner',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Barcode Input Field
          TextField(
            controller: _controller,
            decoration: InputDecoration(
              hintText: 'Enter or scan barcode...',
              prefixIcon: const Icon(Icons.qr_code),
              border: const OutlineInputBorder(),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _controller.clear();
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
            ),
            onSubmitted: (_) => _onManualEntry(),
            onChanged: (value) {
              setState(() {}); // Rebuild to show/hide clear button
            },
          ),
          const SizedBox(height: 12),
          
          // Action Buttons
          Row(
            children: [
              // Scan Button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isScanning ? null : _scanBarcode,
                  icon: _isScanning
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.camera_alt),
                  label: Text(_isScanning ? 'Scanning...' : 'Scan'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              
              // Manual Entry Button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _controller.text.trim().isEmpty ? null : _onManualEntry,
                  icon: const Icon(Icons.keyboard),
                  label: const Text('Add'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // Sample Barcode Button (for testing)
          SizedBox(
            width: double.infinity,
            child: TextButton.icon(
              onPressed: _generateSampleBarcode,
              icon: const Icon(Icons.science, size: 16),
              label: const Text('Generate Sample Barcode'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
              ),
            ),
          ),
          
          // Validation Info
          if (_controller.text.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Icon(
                    BarcodeScannerService.isValidBarcode(_controller.text)
                        ? Icons.check_circle
                        : Icons.error,
                    size: 16,
                    color: BarcodeScannerService.isValidBarcode(_controller.text)
                        ? Colors.green
                        : Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    BarcodeScannerService.isValidBarcode(_controller.text)
                        ? 'Valid barcode format'
                        : 'Invalid barcode format',
                    style: TextStyle(
                      fontSize: 12,
                      color: BarcodeScannerService.isValidBarcode(_controller.text)
                          ? Colors.green
                          : Colors.red,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
