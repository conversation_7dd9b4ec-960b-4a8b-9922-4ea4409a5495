# Development Setup Guide

## Hot Reload Development Environment

### Quick Start Commands

```bash
# 1. Get dependencies
flutter pub get

# 2. Start development server with hot reload
flutter run -d macos --hot

# Alternative: Web development
flutter run -d chrome --hot --web-renderer html
```

### IDE Configuration for Optimal Hot Reload

#### VS Code Setup:
1. Install extensions:
   - Flutter
   - Dart
   - Flutter Widget Snippets

2. Add to VS Code settings.json:
```json
{
  "dart.flutterHotReloadOnSave": "always",
  "dart.flutterHotRestartOnSave": "never",
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true
}
```

#### Android Studio Setup:
1. Install Flutter plugin
2. Enable auto-save: File → Settings → Appearance & Behavior → System Settings → Autosave
3. Set hot reload on save in Flutter settings

### Hot Reload Keyboard Shortcuts

| Action | VS Code | Android Studio | Terminal |
|--------|---------|----------------|----------|
| Hot Reload | Ctrl+S (save) | Ctrl+\ | r |
| Hot Restart | Ctrl+Shift+P → "Flutter: Hot Restart" | Ctrl+Shift+\ | R |
| Stop App | Ctrl+Shift+P → "Flutter: Stop" | Stop button | q |

## Development Workflow

### 1. Starting Development Session
```bash
# Terminal 1: Start the app
flutter run -d macos --hot

# Terminal 2: Watch for file changes (optional)
flutter packages pub run build_runner watch
```

### 2. Making UI Changes
1. Navigate to the file you want to edit
2. Make your changes
3. Save the file (Ctrl+S / Cmd+S)
4. See changes instantly in the running app

### 3. Common Development Tasks

#### Changing POS Interface:
- File: `lib/features/sales/presentation/pages/pos_sales_page.dart`
- Hot reload: ✅ Instant
- Changes: Colors, layouts, text, spacing

#### Modifying Navigation:
- File: `lib/presentation/pages/app_shell.dart`
- Hot reload: ✅ Instant
- Changes: Menu items, icons, layout

#### Adding New Widgets:
- Location: `lib/presentation/widgets/`
- Hot reload: ✅ Instant
- Import and use in existing pages

#### Theme Changes:
- File: `lib/main.dart` (lines 75-78)
- Hot reload: ⚠️ May require hot restart (R)

### 4. Debugging Tips

#### If Hot Reload Stops Working:
1. Press `R` for hot restart
2. If that fails, stop (q) and restart
3. Check terminal for error messages

#### Common Issues:
- **Syntax errors**: Fix and save again
- **Import errors**: Add missing imports
- **State issues**: Use hot restart (R)
- **New dependencies**: Full restart required

## File Structure for UI Development

### Primary UI Files (Most Frequently Modified):
```
lib/features/sales/presentation/pages/pos_sales_page.dart  # Main POS interface
lib/presentation/pages/app_shell.dart                      # Navigation & layout
lib/presentation/widgets/                                  # Reusable components
├── product_tile.dart                                      # Product display
├── cart_item_widget.dart                                  # Cart items
└── simple_barcode_scanner_widget.dart                     # Scanner input
```

### Theme & Styling:
```
lib/main.dart                                              # Main theme
lib/core/theme/                                           # Theme utilities
```

### Business Logic (Modify Carefully):
```
lib/features/*/presentation/bloc/                          # State management
lib/data/                                                  # Data layer
lib/domain/                                               # Business rules
```

## Performance Tips for Hot Reload

### Do's:
- ✅ Save frequently to see changes
- ✅ Use const constructors where possible
- ✅ Keep widgets small and focused
- ✅ Use StatelessWidget when possible

### Don'ts:
- ❌ Don't modify main() function frequently
- ❌ Don't change app initialization code
- ❌ Don't modify dependency injection during development
- ❌ Don't change database schema without restart

## Quick UI Iteration Examples

### Example 1: Change Product Grid Layout
```dart
// In pos_sales_page.dart, line ~179
gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
  crossAxisCount: 4,        // Change from 3 to 4
  childAspectRatio: 0.9,    // Adjust aspect ratio
  crossAxisSpacing: 8,      // Reduce spacing
  mainAxisSpacing: 8,
),
```
Save → See changes instantly!

### Example 2: Modify Cart Width
```dart
// In pos_sales_page.dart, line ~201
Container(
  width: 450,  // Change from 400 to 450
  decoration: BoxDecoration(
```
Save → See changes instantly!

### Example 3: Update Colors
```dart
// In pos_sales_page.dart, line ~136
backgroundColor: Colors.green,  // Change from Theme.of(context).primaryColor
```
Save → See changes instantly!

## Troubleshooting

### Hot Reload Not Working?
1. Check terminal for errors
2. Ensure file is saved
3. Try hot restart (R)
4. Restart if needed (q, then flutter run)

### App Crashes After Changes?
1. Check for syntax errors
2. Verify imports are correct
3. Use hot restart (R)
4. Check terminal error messages

### Performance Issues?
1. Use `flutter run --profile` for performance testing
2. Check for unnecessary rebuilds
3. Use const constructors
4. Profile with Flutter Inspector

---

**Ready to develop!** Start with `flutter run -d macos --hot` and begin making changes!
