MANUAL FLUTTER SETUP - STEP BY STEP

Flutter is downloaded but needs to be extracted and added to PATH. Follow these steps:

STEP 1: Extract Flutter
cd /tmp
unzip -q flutter_macos.zip

STEP 2: Add to PATH for current session
export PATH="$PATH:/tmp/flutter/bin"

STEP 3: Verify Flutter works
flutter --version

STEP 4: Setup your project
cd "/Users/<USER>/Desktop/new kilo"
flutter pub get
flutter config --enable-macos-desktop
flutter run -d macos

ALTERNATIVE - Use the quick script:
cd "/Users/<USER>/Desktop/new kilo" && ./quick_setup.sh