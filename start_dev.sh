#!/bin/bash

# POS Desktop Pro - Development Startup Script
# This script sets up the optimal development environment with hot reload

echo "🚀 Starting POS Desktop Pro Development Environment"
echo "=================================================="

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    echo "Please install Flutter: https://docs.flutter.dev/get-started/install"
    exit 1
fi

# Check Flutter version
echo "📋 Checking Flutter version..."
flutter --version

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Check for connected devices
echo "📱 Checking available devices..."
flutter devices

# Ask user which platform to run on
echo ""
echo "🎯 Select development platform:"
echo "1) macOS (recommended for desktop POS)"
echo "2) Chrome (web development)"
echo "3) Show all devices"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo "🖥️  Starting on macOS with hot reload..."
        flutter run -d macos --hot
        ;;
    2)
        echo "🌐 Starting on Chrome with hot reload..."
        flutter run -d chrome --hot --web-renderer html
        ;;
    3)
        echo "📋 Available devices:"
        flutter devices
        echo ""
        read -p "Enter device ID: " device_id
        echo "🚀 Starting on $device_id with hot reload..."
        flutter run -d "$device_id" --hot
        ;;
    *)
        echo "❌ Invalid choice. Starting on macOS by default..."
        flutter run -d macos --hot
        ;;
esac

echo ""
echo "🎉 Development session ended."
echo "💡 Tips for next time:"
echo "   - Use 'r' for hot reload"
echo "   - Use 'R' for hot restart"
echo "   - Use 'q' to quit"
echo "   - Save files (Ctrl+S) to trigger hot reload"
