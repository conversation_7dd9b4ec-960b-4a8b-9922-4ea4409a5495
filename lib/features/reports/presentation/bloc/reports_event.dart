part of 'reports_bloc.dart';

abstract class ReportsEvent extends Equatable {
  const ReportsEvent();

  @override
  List<Object> get props => [];
}

class LoadReports extends ReportsEvent {
  const LoadReports();
}

class LoadDailySales extends ReportsEvent {
  const LoadDailySales();
}

class LoadWeeklySales extends ReportsEvent {
  const LoadWeeklySales();
}

class LoadMonthlySales extends ReportsEvent {
  const LoadMonthlySales();
}

class LoadSalesByDateRange extends ReportsEvent {
  final DateTime startDate;
  final DateTime endDate;

  const LoadSalesByDateRange(this.startDate, this.endDate);

  @override
  List<Object> get props => [startDate, endDate];
}

class LoadTopProducts extends ReportsEvent {
  const LoadTopProducts();
}

class LoadLowStockProducts extends ReportsEvent {
  const LoadLowStockProducts();
}