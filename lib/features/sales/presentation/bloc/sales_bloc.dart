import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

import '../../../../data/models/product_model.dart';
import '../../../../data/models/sale_model.dart';
import '../../../../domain/repositories/sales_repository.dart';

part 'sales_event.dart';
part 'sales_state.dart';

class CartItem {
  final ProductModel product;
  final int quantity;
  final double price;

  const CartItem({
    required this.product,
    required this.quantity,
    required this.price,
  });

  Map<String, dynamic> toJson() => {
        'product': product.toJson(),
        'quantity': quantity,
        'price': price,
      };

  static CartItem fromJson(Map<String, dynamic> json) => CartItem(
        product: ProductModel.fromJson(json['product']),
        quantity: json['quantity'] as int,
        price: (json['price'] as num).toDouble(),
      );

  CartItem copyWith({
    ProductModel? product,
    int? quantity,
    double? price,
  }) {
    return CartItem(
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
    );
  }
}

class SalesBloc extends HydratedBloc<SalesEvent, SalesState> {
  final SalesRepository salesRepository;

  SalesBloc(this.salesRepository) : super(const SalesState()) {
    on<LoadSales>(_onLoadSales);
    on<AddSale>(_onAddSale);
    on<UpdateSale>(_onUpdateSale);
    on<DeleteSale>(_onDeleteSale);
    on<AddToCart>(_onAddToCart);
    on<RemoveFromCart>(_onRemoveFromCart);
    on<UpdateCartItemQuantity>(_onUpdateCartItemQuantity);
    on<ClearCart>(_onClearCart);
    on<ProcessSale>(_onProcessSale);
  }

  Future<void> _onLoadSales(
    LoadSales event,
    Emitter<SalesState> emit,
  ) async {
    emit(state.copyWith(status: SalesStatus.loading));
    try {
      final sales = await salesRepository.getSales();
      emit(state.copyWith(
        status: SalesStatus.loaded,
        sales: sales,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SalesStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onAddSale(
    AddSale event,
    Emitter<SalesState> emit,
  ) async {
    emit(state.copyWith(status: SalesStatus.loading));
    try {
      await salesRepository.addSale(event.sale);
      final sales = await salesRepository.getSales();
      emit(state.copyWith(
        status: SalesStatus.loaded,
        sales: sales,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SalesStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onUpdateSale(
    UpdateSale event,
    Emitter<SalesState> emit,
  ) async {
    emit(state.copyWith(status: SalesStatus.loading));
    try {
      await salesRepository.updateSale(event.sale);
      final sales = await salesRepository.getSales();
      emit(state.copyWith(
        status: SalesStatus.loaded,
        sales: sales,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SalesStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onDeleteSale(
    DeleteSale event,
    Emitter<SalesState> emit,
  ) async {
    emit(state.copyWith(status: SalesStatus.loading));
    try {
      await salesRepository.deleteSale(event.id);
      final sales = await salesRepository.getSales();
      emit(state.copyWith(
        status: SalesStatus.loaded,
        sales: sales,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SalesStatus.error,
        error: e.toString(),
      ));
    }
  }

  void _onAddToCart(
    AddToCart event,
    Emitter<SalesState> emit,
  ) {
    final currentCart = List<CartItem>.from(state.cart);
    final existingItemIndex = currentCart.indexWhere(
      (item) => item.product.id == event.product.id,
    );

    if (existingItemIndex >= 0) {
      final updatedItem = currentCart[existingItemIndex].copyWith(
        quantity: currentCart[existingItemIndex].quantity + 1,
      );
      currentCart[existingItemIndex] = updatedItem;
    } else {
      final newItem = CartItem(
        product: event.product,
        quantity: 1,
        price: event.product.price,
      );
      currentCart.add(newItem);
    }

    emit(state.copyWith(cart: currentCart));
  }

  void _onRemoveFromCart(
    RemoveFromCart event,
    Emitter<SalesState> emit,
  ) {
    final currentCart = List<CartItem>.from(state.cart);
    currentCart.removeWhere((item) => item.product.id == event.productId);
    emit(state.copyWith(cart: currentCart));
  }

  void _onUpdateCartItemQuantity(
    UpdateCartItemQuantity event,
    Emitter<SalesState> emit,
  ) {
    final currentCart = List<CartItem>.from(state.cart);
    final itemIndex = currentCart.indexWhere(
      (item) => item.product.id == event.productId,
    );

    if (itemIndex >= 0) {
      if (event.quantity <= 0) {
        currentCart.removeAt(itemIndex);
      } else {
        currentCart[itemIndex] = currentCart[itemIndex].copyWith(
          quantity: event.quantity,
        );
      }
    }

    emit(state.copyWith(cart: currentCart));
  }

  void _onClearCart(
    ClearCart event,
    Emitter<SalesState> emit,
  ) {
    emit(state.copyWith(cart: []));
  }

  Future<void> _onProcessSale(
    ProcessSale event,
    Emitter<SalesState> emit,
  ) async {
    emit(state.copyWith(status: SalesStatus.loading));
    try {
      final subtotal = state.cart.fold<double>(
        0.0,
        (sum, item) => sum + (item.price * item.quantity),
      );

      final tax = subtotal * 0.1; // 10% tax
      final total = subtotal + tax;

      final saleItems = state.cart.map((cartItem) => SaleItemModel(
        productId: cartItem.product.id,
        productName: cartItem.product.name,
        price: cartItem.price,
        quantity: cartItem.quantity,
        subtotal: cartItem.price * cartItem.quantity,
      )).toList();

      final sale = SaleModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: event.cashierId,
        userName: 'Cashier', // TODO: Get from auth state
        items: saleItems,
        subtotal: subtotal,
        tax: tax,
        total: total,
        discount: 0.0,
        paymentMethod: event.paymentMethod,
        cashReceived: total, // TODO: Get from payment dialog
        change: 0.0, // TODO: Calculate change
        timestamp: DateTime.now(),
      );

      await salesRepository.addSale(sale);
      
      final sales = await salesRepository.getSales();
      emit(state.copyWith(
        status: SalesStatus.loaded,
        sales: sales,
        cart: [],
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SalesStatus.error,
        error: e.toString(),
      ));
    }
  }

  @override
  SalesState? fromJson(Map<String, dynamic> json) {
    try {
      return SalesState(
        status: SalesStatus.values.firstWhere(
          (e) => e.toString() == json['status'],
          orElse: () => SalesStatus.initial,
        ),
        sales: (json['sales'] as List)
            .map((e) => SaleModel.fromJson(e))
            .toList(),
        cart: (json['cart'] as List)
            .map((e) => CartItem.fromJson(e))
            .toList(),
      );
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(SalesState state) {
    return {
      'status': state.status.toString(),
      'sales': state.sales.map((e) => e.toJson()).toList(),
      'cart': state.cart.map((e) => e.toJson()).toList(),
    };
  }
}

extension CartItemExtension on CartItem {
  Map<String, dynamic> toJson() => {
        'product': product.toJson(),
        'quantity': quantity,
        'price': price,
      };

  static CartItem fromJson(Map<String, dynamic> json) => CartItem(
        product: ProductModel.fromJson(json['product']),
        quantity: json['quantity'],
        price: json['price'],
      );
}