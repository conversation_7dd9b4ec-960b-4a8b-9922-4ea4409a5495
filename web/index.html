<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <meta name="description" content="POS Desktop Pro - A production-grade point-of-sale system">
  <meta name="keywords" content="POS, Point of Sale, Retail, Inventory, Sales">
  
  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="POS Desktop Pro">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>POS Desktop Pro</title>
  <link rel="manifest" href="manifest.json">

  <!-- Use the new initialization API -->
  <script>
    const serviceWorkerVersion = "{{flutter_service_worker_version}}";
  </script>
  <script src="flutter.js" defer></script>
</head>
<body>
  <script>
    window.addEventListener('load', async () => {
      try {
        const loader = _flutter?.loader ?? window.flutterLoader ?? null;
        if (!loader) {
          console.error('Flutter web loader not found');
          return;
        }

        // New API per https://docs.flutter.dev/platform-integration/web/initialization
        const config = {
          entryPointUrl: 'main_web.dart.js',
          serviceWorker: { serviceWorkerVersion },
        };

        // Prefer new unified load, fallback for older SDKs
        let appRunner;
        if (typeof loader.load === 'function') {
          const engineInitializer = await loader.load(config);
          appRunner = await engineInitializer.initializeEngine();
        } else if (typeof loader.loadEntrypoint === 'function') {
          // Backward compatibility
          await loader.loadEntrypoint({
            entrypointUrl: config.entryPointUrl,
            serviceWorker: config.serviceWorker,
            onEntrypointLoaded: async (engineInitializer) => {
              appRunner = await engineInitializer.initializeEngine();
            },
          });
        } else {
          console.error('No supported Flutter loader method found');
          return;
        }

        if (appRunner && typeof appRunner.runApp === 'function') {
          appRunner.runApp();
        } else if (typeof runApp === 'function') {
          runApp();
        } else {
          console.error('Unable to run Flutter web app: appRunner missing');
        }
      } catch (e) {
        console.error('Flutter web bootstrap error:', e);
      }
    });
  </script>
</body>
</html>
