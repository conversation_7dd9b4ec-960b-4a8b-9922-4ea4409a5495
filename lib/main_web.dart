import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/di/injection_container.dart' as di;
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/inventory/presentation/bloc/inventory_bloc.dart';
import 'features/sales/presentation/bloc/sales_bloc.dart';
import 'features/reports/presentation/bloc/reports_bloc.dart';
import 'presentation/pages/app_shell.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize dependency injection
  await di.init();
  
  // Initialize HydratedBloc with web-compatible storage
  try {
    final storage = await HydratedStorage.build(
      storageDirectory: await getApplicationDocumentsDirectory(),
    );
    HydratedBloc.storage = storage;
  } catch (e) {
    // Fallback for web when path_provider fails
    final prefs = await SharedPreferences.getInstance();
    HydratedBloc.storage = _WebStorage(prefs);
  }

  runApp(const MyApp());
}

// Web-compatible storage implementation
class _WebStorage implements Storage {
  final SharedPreferences _prefs;
  
  _WebStorage(this._prefs);

  @override
  dynamic read(String key) {
    final value = _prefs.getString(key);
    if (value == null) return null;
    try {
      return jsonDecode(value);
    } catch (e) {
      return value;
    }
  }

  @override
  Future<void> write(String key, dynamic value) async {
    await _prefs.setString(key, jsonEncode(value));
  }

  @override
  Future<void> delete(String key) async {
    await _prefs.remove(key);
  }

  @override
  Future<void> clear() async {
    await _prefs.clear();
  }

  @override
  Future<void> close() async {
    // SharedPreferences doesn't need explicit closing
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => di.sl<AuthBloc>()),
        BlocProvider(create: (_) => di.sl<InventoryBloc>()),
        BlocProvider(create: (_) => di.sl<SalesBloc>()),
        BlocProvider(create: (_) => di.sl<ReportsBloc>()),
      ],
      child: MaterialApp(
        title: 'POS Desktop Pro',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const AppShell(),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en', 'US'),
          Locale('es', 'ES'),
          Locale('fr', 'FR'),
        ],
      ),
    );
  }
}