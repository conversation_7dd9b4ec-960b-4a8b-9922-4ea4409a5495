import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

import '../../../../data/models/product_model.dart';
import '../../../../data/models/sale_model.dart';
import '../../../../domain/repositories/inventory_repository.dart';
import '../../../../domain/repositories/sales_repository.dart';

part 'reports_event.dart';
part 'reports_state.dart';

class ReportsBloc extends HydratedBloc<ReportsEvent, ReportsState> {
  final SalesRepository salesRepository;
  final InventoryRepository inventoryRepository;

  ReportsBloc({
    required this.salesRepository,
    required this.inventoryRepository,
  }) : super(const ReportsState()) {
    on<LoadReports>(_onLoadReports);
    on<LoadDailySales>(_onLoadDailySales);
    on<LoadWeeklySales>(_onLoadWeeklySales);
    on<LoadMonthlySales>(_onLoadMonthlySales);
    on<LoadSalesByDateRange>(_onLoadSalesByDateRange);
    on<LoadTopProducts>(_onLoadTopProducts);
    on<LoadLowStockProducts>(_onLoadLowStockProducts);
  }

  Future<void> _onLoadReports(
    LoadReports event,
    Emitter<ReportsState> emit,
  ) async {
    emit(state.copyWith(status: ReportsStatus.loading));
    try {
      final sales = await salesRepository.getSales();
      final products = await inventoryRepository.getProducts();

      emit(state.copyWith(
        status: ReportsStatus.loaded,
        sales: sales,
        products: products,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ReportsStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadDailySales(
    LoadDailySales event,
    Emitter<ReportsState> emit,
  ) async {
    emit(state.copyWith(status: ReportsStatus.loading));
    try {
      final sales = await salesRepository.getSales();
      final today = DateTime.now();
      final dailySales = sales.where((sale) {
        return sale.timestamp.year == today.year &&
            sale.timestamp.month == today.month &&
            sale.timestamp.day == today.day;
      }).toList();

      emit(state.copyWith(
        status: ReportsStatus.loaded,
        sales: dailySales,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ReportsStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadWeeklySales(
    LoadWeeklySales event,
    Emitter<ReportsState> emit,
  ) async {
    emit(state.copyWith(status: ReportsStatus.loading));
    try {
      final sales = await salesRepository.getSales();
      final now = DateTime.now();
      final weekAgo = now.subtract(const Duration(days: 7));
      final weeklySales = sales.where((sale) {
        return sale.timestamp.isAfter(weekAgo) &&
            sale.timestamp.isBefore(now);
      }).toList();

      emit(state.copyWith(
        status: ReportsStatus.loaded,
        sales: weeklySales,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ReportsStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadMonthlySales(
    LoadMonthlySales event,
    Emitter<ReportsState> emit,
  ) async {
    emit(state.copyWith(status: ReportsStatus.loading));
    try {
      final sales = await salesRepository.getSales();
      final now = DateTime.now();
      final monthlySales = sales.where((sale) {
        return sale.timestamp.year == now.year &&
            sale.timestamp.month == now.month;
      }).toList();

      emit(state.copyWith(
        status: ReportsStatus.loaded,
        sales: monthlySales,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ReportsStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadSalesByDateRange(
    LoadSalesByDateRange event,
    Emitter<ReportsState> emit,
  ) async {
    emit(state.copyWith(status: ReportsStatus.loading));
    try {
      final sales = await salesRepository.getSales();
      final filteredSales = sales.where((sale) {
        return sale.timestamp.isAfter(event.startDate) &&
            sale.timestamp.isBefore(event.endDate);
      }).toList();

      emit(state.copyWith(
        status: ReportsStatus.loaded,
        sales: filteredSales,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ReportsStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadTopProducts(
    LoadTopProducts event,
    Emitter<ReportsState> emit,
  ) async {
    emit(state.copyWith(status: ReportsStatus.loading));
    try {
      final sales = await salesRepository.getSales();
      final products = await inventoryRepository.getProducts();

      final productSales = <String, int>{};
      for (final sale in sales) {
        for (final item in sale.items) {
          productSales.update(
            item.productId,
            (value) => value + item.quantity,
            ifAbsent: () => item.quantity,
          );
        }
      }

      final sortedProducts = products.where((product) {
        return productSales.containsKey(product.id);
      }).toList()
        ..sort((a, b) {
          final salesA = productSales[a.id] ?? 0;
          final salesB = productSales[b.id] ?? 0;
          return salesB.compareTo(salesA);
        });

      emit(state.copyWith(
        status: ReportsStatus.loaded,
        sales: sales,
        products: sortedProducts.take(10).toList(),
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ReportsStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadLowStockProducts(
    LoadLowStockProducts event,
    Emitter<ReportsState> emit,
  ) async {
    emit(state.copyWith(status: ReportsStatus.loading));
    try {
      final products = await inventoryRepository.getLowStockProducts();
      final sales = await salesRepository.getSales();

      emit(state.copyWith(
        status: ReportsStatus.loaded,
        sales: sales,
        products: products,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ReportsStatus.error,
        error: e.toString(),
      ));
    }
  }

  @override
  ReportsState? fromJson(Map<String, dynamic> json) {
    try {
      return ReportsState(
        status: ReportsStatus.values.firstWhere(
          (e) => e.toString() == json['status'],
          orElse: () => ReportsStatus.initial,
        ),
        sales: (json['sales'] as List)
            .map((e) => SaleModel.fromJson(e))
            .toList(),
        products: (json['products'] as List)
            .map((e) => ProductModel.fromJson(e))
            .toList(),
      );
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(ReportsState state) {
    return {
      'status': state.status.toString(),
      'sales': state.sales.map((e) => e.toJson()).toList(),
      'products': state.products.map((e) => e.toJson()).toList(),
    };
  }
}