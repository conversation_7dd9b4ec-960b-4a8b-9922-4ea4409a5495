part of 'inventory_bloc.dart';

enum InventoryStatus { initial, loading, loaded, error }

class InventoryState extends Equatable {
  final InventoryStatus status;
  final List<ProductModel> products;
  final String? searchQuery;
  final String? selectedCategory;
  final String? error;

  const InventoryState({
    this.status = InventoryStatus.initial,
    this.products = const [],
    this.searchQuery,
    this.selectedCategory,
    this.error,
  });

  InventoryState copyWith({
    InventoryStatus? status,
    List<ProductModel>? products,
    String? searchQuery,
    String? selectedCategory,
    String? error,
  }) {
    return InventoryState(
      status: status ?? this.status,
      products: products ?? this.products,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        status,
        products,
        searchQuery,
        selectedCategory,
        error,
      ];
}