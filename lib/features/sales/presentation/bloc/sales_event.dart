part of 'sales_bloc.dart';

abstract class SalesEvent extends Equatable {
  const SalesEvent();

  @override
  List<Object> get props => [];
}

class LoadSales extends SalesEvent {
  const LoadSales();
}

class AddSale extends SalesEvent {
  final SaleModel sale;

  const AddSale(this.sale);

  @override
  List<Object> get props => [sale];
}

class UpdateSale extends SalesEvent {
  final SaleModel sale;

  const UpdateSale(this.sale);

  @override
  List<Object> get props => [sale];
}

class DeleteSale extends SalesEvent {
  final String id;

  const DeleteSale(this.id);

  @override
  List<Object> get props => [id];
}

class AddToCart extends SalesEvent {
  final ProductModel product;

  const AddToCart(this.product);

  @override
  List<Object> get props => [product];
}

class RemoveFromCart extends SalesEvent {
  final String productId;

  const RemoveFromCart(this.productId);

  @override
  List<Object> get props => [productId];
}

class UpdateCartItemQuantity extends SalesEvent {
  final String productId;
  final int quantity;

  const UpdateCartItemQuantity(this.productId, this.quantity);

  @override
  List<Object> get props => [productId, quantity];
}

class ClearCart extends SalesEvent {
  const ClearCart();
}

class ProcessSale extends SalesEvent {
  final String paymentMethod;
  final String cashierId;

  const ProcessSale({
    required this.paymentMethod,
    required this.cashierId,
  });

  @override
  List<Object> get props => [paymentMethod, cashierId];
}