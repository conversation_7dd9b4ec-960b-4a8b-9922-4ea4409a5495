import '../../domain/repositories/auth_repository.dart';
import '../datasources/local/auth_local_datasource.dart';
import '../models/user_model.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthLocalDataSource localDataSource;

  AuthRepositoryImpl(this.localDataSource);

  @override
  Future<UserModel?> login(String username, String password) async {
    return await localDataSource.login(username, password);
  }

  @override
  Future<bool> logout() async {
    return await localDataSource.logout();
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    return await localDataSource.getCurrentUser();
  }

  @override
  Future<List<UserModel>> getUsers() async {
    return await localDataSource.getUsers();
  }

  @override
  Future<bool> addUser(UserModel user) async {
    return await localDataSource.addUser(user);
  }

  @override
  Future<bool> updateUser(UserModel user) async {
    return await localDataSource.updateUser(user);
  }

  @override
  Future<bool> deleteUser(String id) async {
    return await localDataSource.deleteUser(id);
  }

  @override
  Future<bool> isAuthenticated() async {
    final user = await getCurrentUser();
    return user != null;
  }
}