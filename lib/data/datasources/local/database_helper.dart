import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() {
    return _instance;
  }

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final path = await getDatabasesPath();
    final databasePath = join(path, 'pos_desktop.db');

    return await openDatabase(
      databasePath,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL,
        fullName TEXT,
        email TEXT,
        is_logged_in INTEGER DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        color TEXT,
        isActive INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        barcode TEXT UNIQUE NOT NULL,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        stock INTEGER NOT NULL,
        minStock INTEGER NOT NULL,
        categoryId TEXT NOT NULL,
        imageUrl TEXT,
        isActive INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (categoryId) REFERENCES categories (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE sales (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        userName TEXT NOT NULL,
        items TEXT NOT NULL,
        subtotal REAL NOT NULL,
        tax REAL NOT NULL,
        total REAL NOT NULL,
        discount REAL NOT NULL,
        paymentMethod TEXT NOT NULL,
        cashReceived REAL NOT NULL,
        change REAL NOT NULL,
        timestamp TEXT NOT NULL,
        isSynced INTEGER DEFAULT 0,
        syncedAt TEXT,
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    // Insert default admin user
    await db.insert('users', {
      'id': 'admin-001',
      'username': 'admin',
      'password': 'admin123',
      'role': 'admin',
      'fullName': 'Administrator',
      'email': '<EMAIL>',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    });

    // Insert default categories
    await db.insert('categories', {
      'id': 'cat-001',
      'name': 'Electronics',
      'description': 'Electronic devices and accessories',
      'color': '#FF6B6B',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    });

    await db.insert('categories', {
      'id': 'cat-002',
      'name': 'Food & Beverages',
      'description': 'Food and drink items',
      'color': '#4ECDC4',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    });

    await db.insert('categories', {
      'id': 'cat-003',
      'name': 'Clothing',
      'description': 'Apparel and accessories',
      'color': '#45B7D1',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    });

    // Insert sample products
    await db.insert('products', {
      'id': 'prod-001',
      'name': 'Wireless Mouse',
      'description': 'Ergonomic wireless mouse with USB receiver',
      'barcode': '1234567890123',
      'price': 29.99,
      'cost': 15.00,
      'stock': 50,
      'minStock': 10,
      'categoryId': 'cat-001',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    });

    await db.insert('products', {
      'id': 'prod-002',
      'name': 'Coffee Mug',
      'description': 'Ceramic coffee mug 350ml',
      'barcode': '9876543210987',
      'price': 12.99,
      'cost': 5.00,
      'stock': 100,
      'minStock': 20,
      'categoryId': 'cat-002',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
  }

  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}