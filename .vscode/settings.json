{"dart.flutterHotReloadOnSave": "always", "dart.flutterHotRestartOnSave": "never", "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.evaluateGettersInDebugViews": true, "dart.previewLsp": true, "dart.showTodos": true, "dart.closingLabels": true, "dart.maxLogLineLength": 2000, "dart.insertArgumentPlaceholders": false, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": true, "source.organizeImports": true}, "files.associations": {"*.dart": "dart"}, "emmet.includeLanguages": {"dart": "html"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.dart_tool": true, "**/.packages": true, "**/pubspec.lock": false}, "search.exclude": {"**/build": true, "**/.dart_tool": true, "**/.packages": true}}