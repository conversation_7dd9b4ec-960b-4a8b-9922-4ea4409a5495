import '../../models/product_model.dart';
import '../../models/category_model.dart';
import 'database_helper.dart';

abstract class InventoryLocalDataSource {
  Future<List<ProductModel>> getProducts();
  Future<ProductModel?> getProduct(String id);
  Future<bool> addProduct(ProductModel product);
  Future<bool> updateProduct(ProductModel product);
  Future<bool> deleteProduct(String id);
  Future<List<ProductModel>> searchProducts(String query);
  Future<List<ProductModel>> getProductsByCategory(String categoryId);
  Future<List<ProductModel>> getLowStockProducts();
  Future<List<CategoryModel>> getCategories();
  Future<CategoryModel?> getCategory(String id);
  Future<bool> addCategory(CategoryModel category);
  Future<bool> updateCategory(CategoryModel category);
  Future<bool> deleteCategory(String id);
}

class InventoryLocalDataSourceImpl implements InventoryLocalDataSource {
  final DatabaseHelper databaseHelper;

  InventoryLocalDataSourceImpl(this.databaseHelper);

  @override
  Future<List<ProductModel>> getProducts() async {
    final db = await databaseHelper.database;
    final result = await db.query('products');
    return result.map((e) => ProductModel.fromJson(e)).toList();
  }

  @override
  Future<ProductModel?> getProduct(String id) async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'products',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (result.isNotEmpty) {
      return ProductModel.fromJson(result.first);
    }
    return null;
  }

  @override
  Future<bool> addProduct(ProductModel product) async {
    final db = await databaseHelper.database;
    try {
      await db.insert('products', product.toJson());
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> updateProduct(ProductModel product) async {
    final db = await databaseHelper.database;
    try {
      await db.update(
        'products',
        product.toJson(),
        where: 'id = ?',
        whereArgs: [product.id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteProduct(String id) async {
    final db = await databaseHelper.database;
    try {
      await db.delete(
        'products',
        where: 'id = ?',
        whereArgs: [id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<ProductModel>> searchProducts(String query) async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'products',
      where: 'name LIKE ? OR barcode LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
    );
    return result.map((e) => ProductModel.fromJson(e)).toList();
  }

  @override
  Future<List<ProductModel>> getProductsByCategory(String categoryId) async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'products',
      where: 'categoryId = ?',
      whereArgs: [categoryId],
    );
    return result.map((e) => ProductModel.fromJson(e)).toList();
  }

  @override
  Future<List<ProductModel>> getLowStockProducts() async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'products',
      where: 'stock <= minStock',
    );
    return result.map((e) => ProductModel.fromJson(e)).toList();
  }

  @override
  Future<List<CategoryModel>> getCategories() async {
    final db = await databaseHelper.database;
    final result = await db.query('categories');
    return result.map((e) => CategoryModel.fromJson(e)).toList();
  }

  @override
  Future<CategoryModel?> getCategory(String id) async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (result.isNotEmpty) {
      return CategoryModel.fromJson(result.first);
    }
    return null;
  }

  @override
  Future<bool> addCategory(CategoryModel category) async {
    final db = await databaseHelper.database;
    try {
      await db.insert('categories', category.toJson());
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> updateCategory(CategoryModel category) async {
    final db = await databaseHelper.database;
    try {
      await db.update(
        'categories',
        category.toJson(),
        where: 'id = ?',
        whereArgs: [category.id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteCategory(String id) async {
    final db = await databaseHelper.database;
    try {
      await db.delete(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }
}