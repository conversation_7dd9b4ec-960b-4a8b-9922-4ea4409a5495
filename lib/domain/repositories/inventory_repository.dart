import '../../data/models/product_model.dart';
import '../../data/models/category_model.dart';

abstract class InventoryRepository {
  Future<List<ProductModel>> getProducts();
  Future<ProductModel?> getProduct(String id);
  Future<bool> addProduct(ProductModel product);
  Future<bool> updateProduct(ProductModel product);
  Future<bool> deleteProduct(String id);
  Future<List<ProductModel>> searchProducts(String query);
  Future<List<ProductModel>> getProductsByCategory(String categoryId);
  Future<List<ProductModel>> getLowStockProducts();
  Future<bool> updateStock(String productId, int quantity);
  Future<List<CategoryModel>> getCategories();
  Future<CategoryModel?> getCategory(String id);
  Future<bool> addCategory(CategoryModel category);
  Future<bool> updateCategory(CategoryModel category);
  Future<bool> deleteCategory(String id);
}