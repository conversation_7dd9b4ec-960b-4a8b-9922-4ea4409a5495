import '../../data/models/sale_model.dart';

abstract class SalesRepository {
  Future<List<SaleModel>> getSales();
  Future<SaleModel?> getSale(String id);
  Future<bool> addSale(SaleModel sale);
  Future<bool> updateSale(SaleModel sale);
  Future<bool> deleteSale(String id);
  Future<List<SaleModel>> getSalesByDate(DateTime date);
  Future<List<SaleModel>> getSalesByDateRange(DateTime start, DateTime end);
  Future<List<SaleModel>> getUnsyncedSales();
  Future<bool> markSaleAsSynced(String id);
  Future<double> getTotalSales(DateTime date);
  Future<int> getTotalTransactions(DateTime date);
}