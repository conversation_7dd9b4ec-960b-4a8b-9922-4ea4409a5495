import '../../models/user_model.dart';
import 'database_helper.dart';

abstract class AuthLocalDataSource {
  Future<UserModel?> login(String username, String password);
  Future<bool> logout();
  Future<UserModel?> getCurrentUser();
  Future<List<UserModel>> getUsers();
  Future<bool> addUser(UserModel user);
  Future<bool> updateUser(UserModel user);
  Future<bool> deleteUser(String id);
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final DatabaseHelper databaseHelper;

  AuthLocalDataSourceImpl(this.databaseHelper);

  @override
  Future<UserModel?> login(String username, String password) async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'users',
      where: 'username = ? AND password = ?',
      whereArgs: [username, password],
    );
    
    if (result.isNotEmpty) {
      return UserModel.fromJson(result.first);
    }
    return null;
  }

  @override
  Future<bool> logout() async {
    // In a real app, you might clear session tokens
    return true;
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'users',
      where: 'is_logged_in = ?',
      whereArgs: [1],
    );
    
    if (result.isNotEmpty) {
      return UserModel.fromJson(result.first);
    }
    return null;
  }

  @override
  Future<List<UserModel>> getUsers() async {
    final db = await databaseHelper.database;
    final result = await db.query('users');
    return result.map((e) => UserModel.fromJson(e)).toList();
  }

  @override
  Future<bool> addUser(UserModel user) async {
    final db = await databaseHelper.database;
    try {
      await db.insert('users', user.toJson());
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> updateUser(UserModel user) async {
    final db = await databaseHelper.database;
    try {
      await db.update(
        'users',
        user.toJson(),
        where: 'id = ?',
        whereArgs: [user.id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteUser(String id) async {
    final db = await databaseHelper.database;
    try {
      await db.delete(
        'users',
        where: 'id = ?',
        whereArgs: [id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }
}