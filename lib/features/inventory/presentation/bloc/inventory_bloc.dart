import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

import '../../../../data/models/product_model.dart';
import '../../../../domain/repositories/inventory_repository.dart';

part 'inventory_event.dart';
part 'inventory_state.dart';

class InventoryBloc extends HydratedBloc<InventoryEvent, InventoryState> {
  final InventoryRepository inventoryRepository;

  InventoryBloc(this.inventoryRepository) : super(const InventoryState()) {
    on<LoadProducts>(_onLoadProducts);
    on<AddProduct>(_onAddProduct);
    on<UpdateProduct>(_onUpdateProduct);
    on<DeleteProduct>(_onDeleteProduct);
    on<SearchProducts>(_onSearchProducts);
    on<FilterByCategory>(_onFilterByCategory);
    on<LoadLowStockProducts>(_onLoadLowStockProducts);
  }

  Future<void> _onLoadProducts(
    LoadProducts event,
    Emitter<InventoryState> emit,
  ) async {
    emit(state.copyWith(status: InventoryStatus.loading));
    try {
      final products = await inventoryRepository.getProducts();
      emit(state.copyWith(
        status: InventoryStatus.loaded,
        products: products,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: InventoryStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onAddProduct(
    AddProduct event,
    Emitter<InventoryState> emit,
  ) async {
    emit(state.copyWith(status: InventoryStatus.loading));
    try {
      await inventoryRepository.addProduct(event.product);
      final products = await inventoryRepository.getProducts();
      emit(state.copyWith(
        status: InventoryStatus.loaded,
        products: products,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: InventoryStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onUpdateProduct(
    UpdateProduct event,
    Emitter<InventoryState> emit,
  ) async {
    emit(state.copyWith(status: InventoryStatus.loading));
    try {
      await inventoryRepository.updateProduct(event.product);
      final products = await inventoryRepository.getProducts();
      emit(state.copyWith(
        status: InventoryStatus.loaded,
        products: products,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: InventoryStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onDeleteProduct(
    DeleteProduct event,
    Emitter<InventoryState> emit,
  ) async {
    emit(state.copyWith(status: InventoryStatus.loading));
    try {
      await inventoryRepository.deleteProduct(event.id);
      final products = await inventoryRepository.getProducts();
      emit(state.copyWith(
        status: InventoryStatus.loaded,
        products: products,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: InventoryStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onSearchProducts(
    SearchProducts event,
    Emitter<InventoryState> emit,
  ) async {
    emit(state.copyWith(status: InventoryStatus.loading));
    try {
      final products = await inventoryRepository.searchProducts(event.query);
      emit(state.copyWith(
        status: InventoryStatus.loaded,
        products: products,
        searchQuery: event.query,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: InventoryStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onFilterByCategory(
    FilterByCategory event,
    Emitter<InventoryState> emit,
  ) async {
    emit(state.copyWith(status: InventoryStatus.loading));
    try {
      final products = await inventoryRepository.getProductsByCategory(
        event.categoryId,
      );
      emit(state.copyWith(
        status: InventoryStatus.loaded,
        products: products,
        selectedCategory: event.categoryId,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: InventoryStatus.error,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadLowStockProducts(
    LoadLowStockProducts event,
    Emitter<InventoryState> emit,
  ) async {
    emit(state.copyWith(status: InventoryStatus.loading));
    try {
      final products = await inventoryRepository.getLowStockProducts();
      emit(state.copyWith(
        status: InventoryStatus.loaded,
        products: products,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: InventoryStatus.error,
        error: e.toString(),
      ));
    }
  }

  @override
  InventoryState? fromJson(Map<String, dynamic> json) {
    try {
      return InventoryState(
        status: InventoryStatus.values.firstWhere(
          (e) => e.toString() == json['status'],
          orElse: () => InventoryStatus.initial,
        ),
        products: (json['products'] as List)
            .map((e) => ProductModel.fromJson(e))
            .toList(),
        searchQuery: json['searchQuery'] as String?,
        selectedCategory: json['selectedCategory'] as String?,
      );
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(InventoryState state) {
    return {
      'status': state.status.toString(),
      'products': state.products.map((e) => e.toJson()).toList(),
      'searchQuery': state.searchQuery,
      'selectedCategory': state.selectedCategory,
    };
  }
}