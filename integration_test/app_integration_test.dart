import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:pos_desktop/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('POS Desktop App Integration Tests', () {
    testWidgets('App launches and shows navigation rail', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Verify the app launches successfully
      expect(find.byType(MaterialApp), findsOneWidget);
      
      // Verify navigation rail is present
      expect(find.byType(NavigationRail), findsOneWidget);
      
      // Verify navigation destinations
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Inventory'), findsOneWidget);
      expect(find.text('Sales'), findsOneWidget);
      expect(find.text('Reports'), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);
    });

    testWidgets('Navigation between pages works', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test navigation to Inventory
      await tester.tap(find.text('Inventory'));
      await tester.pumpAndSettle();
      expect(find.text('Inventory Management'), findsOneWidget);

      // Test navigation to Sales
      await tester.tap(find.text('Sales'));
      await tester.pumpAndSettle();
      expect(find.text('Point of Sale'), findsOneWidget);

      // Test navigation to Reports
      await tester.tap(find.text('Reports'));
      await tester.pumpAndSettle();
      expect(find.text('Business Reports'), findsOneWidget);

      // Test navigation back to Dashboard
      await tester.tap(find.text('Dashboard'));
      await tester.pumpAndSettle();
      expect(find.text('Welcome to POS Desktop Pro'), findsOneWidget);
    });
  });
}
