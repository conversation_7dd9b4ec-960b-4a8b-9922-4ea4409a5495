import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:flutter/services.dart';

class BarcodeScannerService {

  /// Scan barcode using the camera
  static Future<String?> scanBarcode() async {
    try {
      final result = await BarcodeScanner.scan();
      if (result.type == ResultType.Barcode) {
        return result.rawContent;
      }
      return null;
    } on PlatformException catch (e) {
      if (e.code == BarcodeScanner.cameraAccessDenied) {
        throw Exception('Camera permission denied');
      } else {
        throw Exception('Unknown error: $e');
      }
    } catch (e) {
      throw Exception('Failed to scan barcode: $e');
    }
  }

  /// Validate barcode format
  static bool isValidBarcode(String barcode) {
    if (barcode.isEmpty) return false;
    
    // Check for common barcode formats
    // UPC-A: 12 digits
    if (barcode.length == 12 && _isNumeric(barcode)) {
      return _validateUPCA(barcode);
    }
    
    // EAN-13: 13 digits
    if (barcode.length == 13 && _isNumeric(barcode)) {
      return _validateEAN13(barcode);
    }
    
    // EAN-8: 8 digits
    if (barcode.length == 8 && _isNumeric(barcode)) {
      return _validateEAN8(barcode);
    }
    
    // Code 128: Variable length alphanumeric
    if (barcode.isNotEmpty && barcode.length <= 48) {
      return _validateCode128(barcode);
    }
    
    return false;
  }

  static bool _isNumeric(String str) {
    return RegExp(r'^[0-9]+$').hasMatch(str);
  }

  static bool _validateUPCA(String barcode) {
    if (barcode.length != 12) return false;
    
    int sum = 0;
    for (int i = 0; i < 11; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(barcode[11]);
  }

  static bool _validateEAN13(String barcode) {
    if (barcode.length != 13) return false;
    
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(barcode[12]);
  }

  static bool _validateEAN8(String barcode) {
    if (barcode.length != 8) return false;
    
    int sum = 0;
    for (int i = 0; i < 7; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit * 3 : digit;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(barcode[7]);
  }

  static bool _validateCode128(String barcode) {
    // Basic validation for Code 128
    // In a real implementation, you would check the actual Code 128 algorithm
    return barcode.isNotEmpty && barcode.length <= 48;
  }

  /// Generate a sample barcode for testing
  static String generateSampleBarcode() {
    // Generate a valid EAN-13 barcode for testing
    String prefix = '123456789012';
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      int digit = int.parse(prefix[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    int checkDigit = (10 - (sum % 10)) % 10;
    return prefix + checkDigit.toString();
  }
}
