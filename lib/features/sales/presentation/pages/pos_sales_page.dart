import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/di/injection_container.dart' as di;
import '../../../../data/models/product_model.dart';
import '../../../../presentation/widgets/product_tile.dart';
import '../../../../presentation/widgets/cart_item_widget.dart';
import '../../../../presentation/widgets/simple_barcode_scanner_widget.dart';
import '../bloc/sales_bloc.dart';
import '../../../../domain/repositories/inventory_repository.dart';

class POSSalesPage extends StatefulWidget {
  const POSSalesPage({super.key});

  @override
  State<POSSalesPage> createState() => _POSSalesPageState();
}

class _POSSalesPageState extends State<POSSalesPage> {
  final TextEditingController _barcodeController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _cartScrollController = ScrollController();
  List<ProductModel> _products = [];
  List<ProductModel> _filteredProducts = [];
  bool _isLoading = true;

  // Pinned products (restrict grid to show only these). Use IDs or barcodes.
  // Replace the sample barcodes with your actual pinned list.
  final Set<String> _pinnedBarcodes = {
    '1234567890123', // Wireless Mouse (sample data)
    '9876543210987', // Coffee Mug (sample data)
  };

  @override
  void initState() {
    super.initState();
    _loadProducts();
    _searchController.addListener(_filterProducts);
  }

  @override
  void dispose() {
    _barcodeController.dispose();
    _searchController.dispose();
    _cartScrollController.dispose();
    super.dispose();
  }

  Future<void> _loadProducts() async {
    try {
      final inventoryRepository = di.sl<InventoryRepository>();
      final products = await inventoryRepository.getProducts();

      // Keep all loaded products for barcode matching in cart,
      // but restrict the grid to pinned products only.
      final pinned = products.where((p) =>
          _pinnedBarcodes.contains(p.barcode) || _pinnedBarcodes.contains(p.id)).toList();

      setState(() {
        _products = products; // full list for barcode scanning/cart
        _filteredProducts = pinned; // grid shows only pinned
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading products: $e')),
        );
      }
    }
  }

  void _filterProducts() {
    // Keep the grid restricted to pinned items; enable text filter within pinned set only.
    final query = _searchController.text.toLowerCase();

    final pinned = _products.where((p) =>
        _pinnedBarcodes.contains(p.barcode) || _pinnedBarcodes.contains(p.id));

    setState(() {
      _filteredProducts = pinned.where((product) {
        if (query.isEmpty) return true;
        return product.name.toLowerCase().contains(query) ||
            product.barcode.toLowerCase().contains(query);
      }).toList();
    });
  }

  void _addToCart(ProductModel product) {
    context.read<SalesBloc>().add(AddToCart(product));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${product.name} added to cart'),
        duration: const Duration(seconds: 1),
      ),
    );
    
    // Auto-scroll to bottom of cart after adding item
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_cartScrollController.hasClients) {
        _cartScrollController.animateTo(
          _cartScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _onBarcodeScanned(String barcode) {
    try {
      final product = _products.firstWhere(
        (p) => p.barcode == barcode,
        orElse: () => throw Exception('Product not found'),
      );
      _addToCart(product);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Product with barcode "$barcode" not found'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Point of Sale'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Row(
        children: [
          // Left side - Products
          Expanded(
            flex: 2,
            child: Column(
              children: [
                // Search Section Only
                Container(
                  padding: const EdgeInsets.all(16),
                  color: Colors.grey[50],
                  child: Column(
                    children: [
                      // Search Bar
                      TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          hintText: 'Search products...',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Products Grid (Pinned products only)
                Expanded(
                  child: _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _filteredProducts.isEmpty
                          ? const Center(
                              child: Text(
                                'No products found',
                                style: TextStyle(fontSize: 16, color: Colors.grey),
                              ),
                            )
                          : GridView.builder(
                              padding: const EdgeInsets.all(16),
                              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                childAspectRatio: 0.8,
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                              ),
                              itemCount: _filteredProducts.length,
                              itemBuilder: (context, index) {
                                final product = _filteredProducts[index];
                                return ProductTile(
                                  product: product,
                                  onTap: () => _addToCart(product),
                                );
                              },
                            ),
                ),
              ],
            ),
          ),
          
          // Right side - Cart
          Container(
            width: 400,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                left: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: _buildCartSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildCartSection() {
    return BlocBuilder<SalesBloc, SalesState>(
      builder: (context, state) {
        return Column(
          children: [
            // Barcode input (USB scanner friendly): keep simple text input for fast scans.
            Container(
              color: Colors.white,
              child: SimpleBarcodeScanner(
                onBarcodeScanned: _onBarcodeScanned,
              ),
            ),
            
            // Cart Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.shopping_cart, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    'Cart (${state.cartItemCount} items)',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (state.cart.isNotEmpty)
                    IconButton(
                      onPressed: () => _showClearCartDialog(context),
                      icon: const Icon(Icons.clear_all, color: Colors.white),
                      tooltip: 'Clear Cart',
                    ),
                ],
              ),
            ),

            // Cart Items
            Expanded(
              child: state.cart.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.shopping_cart_outlined,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Cart is empty',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Add products to get started',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Scrollbar(
                      controller: _cartScrollController,
                      thumbVisibility: true,
                      child: ListView.builder(
                        controller: _cartScrollController,
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        itemCount: state.cart.length,
                        itemBuilder: (context, index) {
                          final cartItem = state.cart[index];
                          return CartItemWidget(
                            cartItem: cartItem,
                            onIncrement: () {
                              context.read<SalesBloc>().add(
                                UpdateCartItemQuantity(
                                  cartItem.product.id,
                                  cartItem.quantity + 1,
                                ),
                              );
                              // Auto-scroll after quantity update
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (_cartScrollController.hasClients) {
                                  _cartScrollController.animateTo(
                                    _cartScrollController.position.maxScrollExtent,
                                    duration: const Duration(milliseconds: 200),
                                    curve: Curves.easeOut,
                                  );
                                }
                              });
                            },
                            onDecrement: () => context.read<SalesBloc>().add(
                              UpdateCartItemQuantity(
                                cartItem.product.id,
                                cartItem.quantity - 1,
                              ),
                            ),
                            onRemove: () => context.read<SalesBloc>().add(
                              RemoveFromCart(cartItem.product.id),
                            ),
                          );
                        },
                      ),
                    ),
            ),

            // Cart Summary and Checkout
            if (state.cart.isNotEmpty) _buildCartSummary(state),
          ],
        );
      },
    );
  }

  Widget _buildCartSummary(SalesState state) {
    final subtotal = state.cartTotal;
    final tax = subtotal * 0.1; // 10% tax
    final total = subtotal + tax;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          // Summary Details
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Subtotal:'),
              Text('\$${subtotal.toStringAsFixed(2)}'),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Tax (10%):'),
              Text('\$${tax.toStringAsFixed(2)}'),
            ],
          ),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '\$${total.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Checkout Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _showCheckoutDialog(context, total),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Checkout',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearCartDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cart'),
        content: const Text('Are you sure you want to clear all items from the cart?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<SalesBloc>().add(const ClearCart());
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showCheckoutDialog(BuildContext context, double total) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Process Sale'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Total: \$${total.toStringAsFixed(2)}'),
            const SizedBox(height: 16),
            const Text('Select payment method:'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<SalesBloc>().add(const ProcessSale(
                paymentMethod: 'Cash',
                cashierId: 'admin-001',
              ));
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Sale processed successfully!')),
              );
            },
            child: const Text('Cash'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<SalesBloc>().add(const ProcessSale(
                paymentMethod: 'Card',
                cashierId: 'admin-001',
              ));
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Sale processed successfully!')),
              );
            },
            child: const Text('Card'),
          ),
        ],
      ),
    );
  }
}
