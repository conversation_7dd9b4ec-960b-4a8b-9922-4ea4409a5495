import 'package:flutter/material.dart';

class SimpleBarcodeScanner extends StatefulWidget {
  final Function(String) onBarcodeScanned;

  const SimpleBarcodeScanner({
    super.key,
    required this.onBarcodeScanned,
  });

  @override
  State<SimpleBarcodeScanner> createState() => _SimpleBarcodeScannerState();
}

class _SimpleBarcodeScannerState extends State<SimpleBarcodeScanner> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onSubmitted(String value) {
    final barcode = value.trim();
    if (barcode.isNotEmpty) {
      widget.onBarcodeScanned(barcode);
      _controller.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: TextField(
        controller: _controller,
        decoration: const InputDecoration(
          hintText: 'Scan or enter barcode...',
          prefixIcon: Icon(Icons.qr_code_scanner, size: 20),
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          isDense: true,
        ),
        onSubmitted: _onSubmitted,
        textInputAction: TextInputAction.done,
      ),
    );
  }
}