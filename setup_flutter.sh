#!/bin/bash

# Flutter Setup Script for POS Desktop Application
# Run this script to complete Flutter installation and launch your POS app

echo "🚀 Setting up Flutter for POS Desktop Pro..."

# Step 1: Extract Flutter
echo "📦 Extracting Flutter SDK..."
cd /tmp
unzip -q flutter_macos.zip
sudo mv flutter /opt/flutter

# Step 2: Add to PATH
echo "🛠️  Configuring PATH..."
echo 'export PATH="$PATH:/opt/flutter/bin"' >> ~/.zshrc
source ~/.zshrc

# Step 3: Verify installation
echo "🔍 Verifying Flutter installation..."
export PATH="$PATH:/opt/flutter/bin"
flutter doctor

# Step 4: Setup project
echo "📋 Setting up POS project..."
cd "/Users/<USER>/Desktop/new kilo"
flutter pub get
flutter config --enable-macos-desktop

# Step 5: Launch application
echo "🎯 Launching POS Desktop Pro..."
flutter run -d macos

echo "✅ Setup complete! Your POS application is running."