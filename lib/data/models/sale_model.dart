import 'package:equatable/equatable.dart';

class SaleItemModel extends Equatable {
  final String productId;
  final String productName;
  final double price;
  final int quantity;
  final double subtotal;

  const SaleItemModel({
    required this.productId,
    required this.productName,
    required this.price,
    required this.quantity,
    required this.subtotal,
  });

  factory SaleItemModel.fromJson(Map<String, dynamic> json) {
    return SaleItemModel(
      productId: json['productId'] as String,
      productName: json['productName'] as String,
      price: (json['price'] as num).toDouble(),
      quantity: json['quantity'] as int,
      subtotal: (json['subtotal'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'price': price,
      'quantity': quantity,
      'subtotal': subtotal,
    };
  }

  @override
  List<Object?> get props => [productId, productName, price, quantity, subtotal];
}

class SaleModel extends Equatable {
  final String id;
  final String userId;
  final String userName;
  final List<SaleItemModel> items;
  final double subtotal;
  final double tax;
  final double total;
  final double discount;
  final String paymentMethod;
  final double cashReceived;
  final double change;
  final DateTime timestamp;
  final bool isSynced;
  final DateTime? syncedAt;

  const SaleModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.total,
    required this.discount,
    required this.paymentMethod,
    required this.cashReceived,
    required this.change,
    required this.timestamp,
    this.isSynced = false,
    this.syncedAt,
  });

  factory SaleModel.fromJson(Map<String, dynamic> json) {
    return SaleModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      items: (json['items'] as List<dynamic>)
          .map((e) => SaleItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      subtotal: (json['subtotal'] as num).toDouble(),
      tax: (json['tax'] as num).toDouble(),
      total: (json['total'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      paymentMethod: json['paymentMethod'] as String,
      cashReceived: (json['cashReceived'] as num).toDouble(),
      change: (json['change'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
      syncedAt: json['syncedAt'] != null
          ? DateTime.parse(json['syncedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'items': items.map((e) => e.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'total': total,
      'discount': discount,
      'paymentMethod': paymentMethod,
      'cashReceived': cashReceived,
      'change': change,
      'timestamp': timestamp.toIso8601String(),
      'isSynced': isSynced,
      'syncedAt': syncedAt?.toIso8601String(),
    };
  }

  SaleModel copyWith({
    String? id,
    String? userId,
    String? userName,
    List<SaleItemModel>? items,
    double? subtotal,
    double? tax,
    double? total,
    double? discount,
    String? paymentMethod,
    double? cashReceived,
    double? change,
    DateTime? timestamp,
    bool? isSynced,
    DateTime? syncedAt,
  }) {
    return SaleModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      total: total ?? this.total,
      discount: discount ?? this.discount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      cashReceived: cashReceived ?? this.cashReceived,
      change: change ?? this.change,
      timestamp: timestamp ?? this.timestamp,
      isSynced: isSynced ?? this.isSynced,
      syncedAt: syncedAt ?? this.syncedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        userName,
        items,
        subtotal,
        tax,
        total,
        discount,
        paymentMethod,
        cashReceived,
        change,
        timestamp,
        isSynced,
        syncedAt,
      ];
}