# POS Desktop Pro

A production-grade, offline-first, cross-platform Point of Sale (POS) desktop application built with Flutter. Designed for rapid UI iteration and real-time development with hot reload capabilities.

## 🚀 Quick Start - Development Mode

### Prerequisites
- Flutter SDK (>=3.8.0)
- Dart SDK
- IDE with Flutter support (VS Code, Android Studio, or IntelliJ)

### Running the App with Hot Reload

1. **Start the development server:**
   ```bash
   flutter run -d macos --hot
   ```

   Or for web development:
   ```bash
   flutter run -d chrome --hot
   ```

2. **Enable hot reload in your IDE:**
   - **VS Code**: Press `Ctrl+S` (Cmd+S on Mac) to save and trigger hot reload
   - **Android Studio**: Press `Ctrl+\` or click the hot reload button
   - **Terminal**: Press `r` in the terminal where Flutter is running

3. **For instant UI changes:**
   - Save any file in the `lib/` directory
   - Changes will appear instantly without losing app state
   - Use `R` (capital R) for hot restart if needed

### Development Workflow

1. **Start the app**: `flutter run -d macos --hot`
2. **Make UI changes** in any file under `lib/presentation/`
3. **Save the file** (Ctrl+S / Cmd+S)
4. **See changes instantly** in the running app
5. **Iterate quickly** without restarting the application

## 🏗️ Project Structure (Optimized for UI Iteration)

```
lib/
├── main.dart                    # App entry point
├── core/                        # Core utilities and services
│   ├── di/                     # Dependency injection
│   ├── theme/                  # App theming
│   └── utils/                  # Utilities
├── data/                       # Data layer
│   ├── models/                 # Data models
│   ├── datasources/           # Data sources
│   └── repositories/          # Repository implementations
├── domain/                     # Business logic layer
│   └── repositories/          # Repository interfaces
├── features/                   # Feature modules
│   ├── auth/                  # Authentication
│   ├── inventory/             # Product management
│   ├── sales/                 # POS sales interface
│   ├── reports/               # Business reports
│   └── settings/              # App settings
└── presentation/              # UI layer (MAIN AREA FOR UI CHANGES)
    ├── pages/                 # Main app pages
    └── widgets/               # Reusable UI components
```

## 🎨 UI Development Guidelines

### For Rapid UI Iteration:

1. **UI Components Location**: All UI components are in `lib/presentation/`
2. **Main POS Interface**: `lib/features/sales/presentation/pages/pos_sales_page.dart`
3. **Reusable Widgets**: `lib/presentation/widgets/`
4. **App Shell & Navigation**: `lib/presentation/pages/app_shell.dart`

### Hot Reload Best Practices:

- **Stateless Widgets**: Changes reload instantly
- **Stateful Widgets**: Most changes reload instantly, some may require hot restart
- **Theme Changes**: Usually require hot restart (`R`)
- **New Dependencies**: Require full restart

## 🛠️ Development Commands

### Essential Commands:
```bash
# Start development with hot reload
flutter run -d macos --hot

# Hot reload (save file or press 'r' in terminal)
r

# Hot restart (press 'R' in terminal)
R

# Stop the app
q

# Get dependencies
flutter pub get

# Clean build files
flutter clean

# Run tests
flutter test

# Build for production
flutter build macos
```

### IDE Setup for Optimal Hot Reload:

#### VS Code:
1. Install Flutter extension
2. Enable auto-save: `File > Auto Save`
3. Use `Ctrl+S` to trigger hot reload
4. Use Command Palette: `Flutter: Hot Reload`

#### Android Studio:
1. Install Flutter plugin
2. Use hot reload button in toolbar
3. Enable auto-save in settings
4. Use `Ctrl+\` for quick hot reload

## 🎯 Key Features

- **Modern POS Interface**: Clean, intuitive design for retail operations
- **Product Management**: Add, edit, and organize inventory
- **Cart Management**: Real-time cart updates with quantity controls
- **Barcode Scanning**: Support for USB barcode scanners
- **Sales Processing**: Cash and card payment options
- **Offline-First**: Works without internet connection
- **Cross-Platform**: Runs on macOS, Windows, Linux, and Web
- **Hot Reload Ready**: Optimized for rapid UI development

## 🔧 Architecture

- **Clean Architecture**: Separation of concerns with clear layers
- **BLoC Pattern**: State management with flutter_bloc
- **Dependency Injection**: Using get_it for loose coupling
- **Repository Pattern**: Abstracted data access
- **Offline Storage**: SQLite with sqflite
- **Responsive Design**: Adapts to different screen sizes

## 📱 Current Features

### ✅ Implemented:
- Navigation rail with main sections
- Product catalog with search
- Shopping cart with quantity management
- Barcode scanner integration
- Basic checkout process
- User authentication
- Offline data storage

### 🚧 Ready for Customization:
- UI themes and styling
- Product grid layout
- Cart interface design
- Checkout flow
- Dashboard widgets
- Reports interface

## 🎨 UI Customization Quick Guide

### Changing Colors:
Edit `lib/main.dart` line 75-78:
```dart
theme: ThemeData(
  colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue), // Change this
  useMaterial3: true,
),
```

### Modifying POS Layout:
Edit `lib/features/sales/presentation/pages/pos_sales_page.dart`

### Updating Navigation:
Edit `lib/presentation/pages/app_shell.dart`

### Adding New Widgets:
Create files in `lib/presentation/widgets/`

## 🚀 Getting Started with UI Changes

1. **Start the app**: `flutter run -d macos --hot`
2. **Open**: `lib/features/sales/presentation/pages/pos_sales_page.dart`
3. **Make a simple change**: Update a text or color
4. **Save the file**: See the change instantly!
5. **Iterate**: Continue making changes and saving

## 📞 Support

For Flutter development help:
- [Flutter Documentation](https://docs.flutter.dev/)
- [Flutter Hot Reload Guide](https://docs.flutter.dev/development/tools/hot-reload)
- [BLoC Pattern Guide](https://bloclibrary.dev/)

---

**Ready to start developing!** Run `flutter run -d macos --hot` and begin making UI changes with instant feedback.
