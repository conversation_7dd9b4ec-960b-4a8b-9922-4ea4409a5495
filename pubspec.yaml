name: pos_desktop
description: A production-grade, offline-first, cross-platform point-of-sale desktop application
version: 1.0.0+1
publish_to: 'none'

environment:
  sdk: '>=3.8.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # Core
  cupertino_icons: ^1.0.6
  equatable: ^2.0.5
  get_it: ^7.6.4
  uuid: ^3.0.7
  
  # State Management
  bloc: ^8.1.4
  flutter_bloc: ^8.1.3
  hydrated_bloc: ^9.1.2
  
  # Database
  sqflite: ^2.3.0
  path: ^1.8.3
  path_provider: ^2.1.1
  
  # Network & Sync
  dio: ^5.3.3
  connectivity_plus: ^5.0.1
  retry: ^3.1.2
  
  # UI & Responsive
  responsive_builder: ^0.7.0
  flutter_screenutil: ^5.9.0
  animations: ^2.0.8
  
  # Barcode & Camera
  camera: ^0.10.5+5
  barcode_scan2: ^4.3.0
  qr_code_scanner: ^1.0.1
  
  # Printing
  printing: ^5.11.1
  pdf: ^3.10.7
  
  # Storage & Files
  shared_preferences: ^2.2.2
  
  # Security
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3
  
  # Logging
  logger: ^2.0.2+1
  
  # Date & Time
  intl: ^0.20.2
  jiffy: ^6.2.1
  
  # Charts & Reports
  fl_chart: ^0.68.0
  data_table_2: ^2.6.0
  
  # Window Management
  window_manager: ^0.3.7
  screen_retriever: ^0.1.9

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  bloc_test: ^9.1.4
  mocktail: ^1.0.1
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/