part of 'sales_bloc.dart';

enum SalesStatus { initial, loading, loaded, error }

class SalesState extends Equatable {
  final SalesStatus status;
  final List<SaleModel> sales;
  final List<CartItem> cart;
  final String? error;

  const SalesState({
    this.status = SalesStatus.initial,
    this.sales = const [],
    this.cart = const [],
    this.error,
  });

  SalesState copyWith({
    SalesStatus? status,
    List<SaleModel>? sales,
    List<CartItem>? cart,
    String? error,
  }) {
    return SalesState(
      status: status ?? this.status,
      sales: sales ?? this.sales,
      cart: cart ?? this.cart,
      error: error ?? this.error,
    );
  }

  double get cartTotal => cart.fold(
        0.0,
        (sum, item) => sum + (item.price * item.quantity),
      );

  int get cartItemCount => cart.fold(
        0,
        (sum, item) => sum + item.quantity,
      );

  @override
  List<Object?> get props => [status, sales, cart, error];
}