import 'dart:convert';
import '../../models/sale_model.dart';
import 'database_helper.dart';

abstract class SalesLocalDataSource {
  Future<List<SaleModel>> getSales();
  Future<SaleModel?> getSale(String id);
  Future<bool> addSale(SaleModel sale);
  Future<bool> updateSale(SaleModel sale);
  Future<bool> deleteSale(String id);
  Future<List<SaleModel>> getSalesByDate(DateTime date);
  Future<List<SaleModel>> getSalesByDateRange(DateTime start, DateTime end);
  Future<List<SaleModel>> getUnsyncedSales();
  Future<bool> markSaleAsSynced(String id);
}

class SalesLocalDataSourceImpl implements SalesLocalDataSource {
  final DatabaseHelper databaseHelper;

  SalesLocalDataSourceImpl(this.databaseHelper);

  @override
  Future<List<SaleModel>> getSales() async {
    final db = await databaseHelper.database;
    final result = await db.query('sales');
    return result.map((e) => _saleFromDb(e)).toList();
  }

  @override
  Future<SaleModel?> getSale(String id) async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'sales',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (result.isNotEmpty) {
      return _saleFromDb(result.first);
    }
    return null;
  }

  @override
  Future<bool> addSale(SaleModel sale) async {
    final db = await databaseHelper.database;
    try {
      await db.insert('sales', _saleToDb(sale));
      
      // Update product stock
      for (final item in sale.items) {
        await db.rawUpdate(
          'UPDATE products SET stock = stock - ? WHERE id = ?',
          [item.quantity, item.productId],
        );
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> updateSale(SaleModel sale) async {
    final db = await databaseHelper.database;
    try {
      await db.update(
        'sales',
        _saleToDb(sale),
        where: 'id = ?',
        whereArgs: [sale.id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteSale(String id) async {
    final db = await databaseHelper.database;
    try {
      await db.delete(
        'sales',
        where: 'id = ?',
        whereArgs: [id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<SaleModel>> getSalesByDate(DateTime date) async {
    final db = await databaseHelper.database;
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final result = await db.query(
      'sales',
      where: 'timestamp >= ? AND timestamp < ?',
      whereArgs: [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
    );
    
    return result.map((e) => _saleFromDb(e)).toList();
  }

  @override
  Future<List<SaleModel>> getSalesByDateRange(DateTime start, DateTime end) async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'sales',
      where: 'timestamp >= ? AND timestamp <= ?',
      whereArgs: [start.toIso8601String(), end.toIso8601String()],
    );
    
    return result.map((e) => _saleFromDb(e)).toList();
  }

  @override
  Future<List<SaleModel>> getUnsyncedSales() async {
    final db = await databaseHelper.database;
    final result = await db.query(
      'sales',
      where: 'isSynced = ?',
      whereArgs: [0],
    );
    
    return result.map((e) => _saleFromDb(e)).toList();
  }

  @override
  Future<bool> markSaleAsSynced(String id) async {
    final db = await databaseHelper.database;
    try {
      await db.update(
        'sales',
        {'isSynced': 1, 'syncedAt': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [id],
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  SaleModel _saleFromDb(Map<String, dynamic> json) {
    return SaleModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      items: (jsonDecode(json['items'] as String) as List<dynamic>)
          .map((e) => SaleItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      subtotal: (json['subtotal'] as num).toDouble(),
      tax: (json['tax'] as num).toDouble(),
      total: (json['total'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      paymentMethod: json['paymentMethod'] as String,
      cashReceived: (json['cashReceived'] as num).toDouble(),
      change: (json['change'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      isSynced: (json['isSynced'] as int) == 1,
      syncedAt: json['syncedAt'] != null
          ? DateTime.parse(json['syncedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> _saleToDb(SaleModel sale) {
    return {
      'id': sale.id,
      'userId': sale.userId,
      'userName': sale.userName,
      'items': jsonEncode(sale.items.map((e) => e.toJson()).toList()),
      'subtotal': sale.subtotal,
      'tax': sale.tax,
      'total': sale.total,
      'discount': sale.discount,
      'paymentMethod': sale.paymentMethod,
      'cashReceived': sale.cashReceived,
      'change': sale.change,
      'timestamp': sale.timestamp.toIso8601String(),
      'isSynced': sale.isSynced ? 1 : 0,
      'syncedAt': sale.syncedAt?.toIso8601String(),
    };
  }
}