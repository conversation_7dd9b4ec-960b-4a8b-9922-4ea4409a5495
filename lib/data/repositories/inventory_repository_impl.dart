import '../../domain/repositories/inventory_repository.dart';
import '../datasources/local/inventory_local_datasource.dart';
import '../models/product_model.dart';
import '../models/category_model.dart';

class InventoryRepositoryImpl implements InventoryRepository {
  final InventoryLocalDataSource localDataSource;

  InventoryRepositoryImpl(this.localDataSource);

  @override
  Future<List<ProductModel>> getProducts() async {
    return await localDataSource.getProducts();
  }

  @override
  Future<ProductModel?> getProduct(String id) async {
    return await localDataSource.getProduct(id);
  }

  @override
  Future<bool> addProduct(ProductModel product) async {
    return await localDataSource.addProduct(product);
  }

  @override
  Future<bool> updateProduct(ProductModel product) async {
    return await localDataSource.updateProduct(product);
  }

  @override
  Future<bool> deleteProduct(String id) async {
    return await localDataSource.deleteProduct(id);
  }

  @override
  Future<List<ProductModel>> searchProducts(String query) async {
    return await localDataSource.searchProducts(query);
  }

  @override
  Future<List<ProductModel>> getProductsByCategory(String categoryId) async {
    return await localDataSource.getProductsByCategory(categoryId);
  }

  @override
  Future<List<ProductModel>> getLowStockProducts() async {
    return await localDataSource.getLowStockProducts();
  }

  @override
  Future<bool> updateStock(String productId, int quantity) async {
    final product = await getProduct(productId);
    if (product != null) {
      final updatedProduct = product.copyWith(stock: product.stock + quantity);
      return await updateProduct(updatedProduct);
    }
    return false;
  }

  @override
  Future<List<CategoryModel>> getCategories() async {
    return await localDataSource.getCategories();
  }

  @override
  Future<CategoryModel?> getCategory(String id) async {
    return await localDataSource.getCategory(id);
  }

  @override
  Future<bool> addCategory(CategoryModel category) async {
    return await localDataSource.addCategory(category);
  }

  @override
  Future<bool> updateCategory(CategoryModel category) async {
    return await localDataSource.updateCategory(category);
  }

  @override
  Future<bool> deleteCategory(String id) async {
    return await localDataSource.deleteCategory(id);
  }
}