import 'package:get_it/get_it.dart';

import '../../data/datasources/local/auth_local_datasource.dart';
import '../../data/datasources/local/database_helper.dart';
import '../../data/datasources/local/inventory_local_datasource.dart';
import '../../data/datasources/local/sales_local_datasource.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/inventory_repository_impl.dart';
import '../../data/repositories/sales_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/inventory_repository.dart';
import '../../domain/repositories/sales_repository.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/inventory/presentation/bloc/inventory_bloc.dart';
import '../../features/reports/presentation/bloc/reports_bloc.dart';
import '../../features/sales/presentation/bloc/sales_bloc.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Database
  final databaseHelper = DatabaseHelper();
  sl.registerLazySingleton<DatabaseHelper>(() => databaseHelper);

  // Data sources
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sl()),
  );
  sl.registerLazySingleton<InventoryLocalDataSource>(
    () => InventoryLocalDataSourceImpl(sl()),
  );
  sl.registerLazySingleton<SalesLocalDataSource>(
    () => SalesLocalDataSourceImpl(sl()),
  );

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl()),
  );
  sl.registerLazySingleton<InventoryRepository>(
    () => InventoryRepositoryImpl(sl()),
  );
  sl.registerLazySingleton<SalesRepository>(
    () => SalesRepositoryImpl(sl()),
  );

  // BLoCs
  sl.registerFactory<AuthBloc>(
    () => AuthBloc(sl()),
  );
  sl.registerFactory<InventoryBloc>(
    () => InventoryBloc(sl()),
  );
  sl.registerFactory<SalesBloc>(
    () => SalesBloc(sl()),
  );
  sl.registerFactory<ReportsBloc>(
    () => ReportsBloc(
      salesRepository: sl(),
      inventoryRepository: sl(),
    ),
  );
}