part of 'reports_bloc.dart';

enum ReportsStatus { initial, loading, loaded, error }

class ReportsState extends Equatable {
  final ReportsStatus status;
  final List<SaleModel> sales;
  final List<ProductModel> products;
  final String? error;

  const ReportsState({
    this.status = ReportsStatus.initial,
    this.sales = const [],
    this.products = const [],
    this.error,
  });

  ReportsState copyWith({
    ReportsStatus? status,
    List<SaleModel>? sales,
    List<ProductModel>? products,
    String? error,
  }) {
    return ReportsState(
      status: status ?? this.status,
      sales: sales ?? this.sales,
      products: products ?? this.products,
      error: error ?? this.error,
    );
  }

  double get totalSales => sales.fold(
        0.0,
        (sum, sale) => sum + sale.total,
      );

  int get totalTransactions => sales.length;

  double get averageTransactionValue =>
      totalTransactions > 0 ? totalSales / totalTransactions : 0.0;

  @override
  List<Object?> get props => [status, sales, products, error];
}