import '../../domain/repositories/sales_repository.dart';
import '../datasources/local/sales_local_datasource.dart';
import '../models/sale_model.dart';

class SalesRepositoryImpl implements SalesRepository {
  final SalesLocalDataSource localDataSource;

  SalesRepositoryImpl(this.localDataSource);

  @override
  Future<List<SaleModel>> getSales() async {
    return await localDataSource.getSales();
  }

  @override
  Future<SaleModel?> getSale(String id) async {
    return await localDataSource.getSale(id);
  }

  @override
  Future<bool> addSale(SaleModel sale) async {
    return await localDataSource.addSale(sale);
  }

  @override
  Future<bool> updateSale(SaleModel sale) async {
    return await localDataSource.updateSale(sale);
  }

  @override
  Future<bool> deleteSale(String id) async {
    return await localDataSource.deleteSale(id);
  }

  @override
  Future<List<SaleModel>> getSalesByDate(DateTime date) async {
    return await localDataSource.getSalesByDate(date);
  }

  @override
  Future<List<SaleModel>> getSalesByDateRange(DateTime start, DateTime end) async {
    return await localDataSource.getSalesByDateRange(start, end);
  }

  @override
  Future<List<SaleModel>> getUnsyncedSales() async {
    return await localDataSource.getUnsyncedSales();
  }

  @override
  Future<bool> markSaleAsSynced(String id) async {
    return await localDataSource.markSaleAsSynced(id);
  }

  @override
  Future<double> getTotalSales(DateTime date) async {
    final sales = await getSalesByDate(date);
    return sales.fold<double>(0.0, (double sum, sale) => sum + sale.total);
  }

  @override
  Future<int> getTotalTransactions(DateTime date) async {
    final sales = await getSalesByDate(date);
    return sales.length;
  }
}